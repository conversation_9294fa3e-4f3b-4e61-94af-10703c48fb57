import React, { useState, useRef, useEffect } from 'react';
import { LocalAttachment } from '@/lib/types';
import { X, FileText, Image as ImageIcon, Edit2, Check, XIcon } from 'lucide-react'; // Using ImageIcon for clarity
import { Button } from './ui/button';

interface AttachmentPillProps {
  attachment: LocalAttachment;
  onRemove: (attachmentId: string) => void;
  onRename?: (attachmentId: string, newName: string) => void;
}

export const AttachmentPill: React.FC<AttachmentPillProps> = ({ attachment, onRemove, onRename }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(attachment.name);
  const inputRef = useRef<HTMLInputElement>(null);

  const isImage = attachment.type.startsWith('image/') && attachment.dataUrl;
  const isText = attachment.type.startsWith('text/') && attachment.textContent;

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      // Select the filename without extension for easier editing
      const lastDotIndex = editedName.lastIndexOf('.');
      if (lastDotIndex > 0) {
        inputRef.current.setSelectionRange(0, lastDotIndex);
      } else {
        inputRef.current.select();
      }
    }
  }, [isEditing, editedName]);

  const handleStartEdit = () => {
    setEditedName(attachment.name);
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    const trimmedName = editedName.trim();
    if (!trimmedName) {
      // Don't allow empty names
      setEditedName(attachment.name);
      setIsEditing(false);
      return;
    }

    // Ensure the file keeps its extension if it's an image
    let finalName = trimmedName;
    if (attachment.type.startsWith('image/')) {
      const originalExt = attachment.name.split('.').pop()?.toLowerCase();
      const newExt = trimmedName.split('.').pop()?.toLowerCase();

      // If the new name doesn't have an extension or has a different one, preserve the original
      if (!newExt || !['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp'].includes(newExt)) {
        if (originalExt && ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp'].includes(originalExt)) {
          finalName = `${trimmedName}.${originalExt}`;
        }
      }
    }

    if (finalName !== attachment.name && onRename) {
      onRename(attachment.id, finalName);
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedName(attachment.name);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  return (
    <div className="group inline-flex items-center bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm mr-2 mb-2 shadow">
      {isImage ? (
        <ImageIcon size={16} className="mr-2 text-gray-600 dark:text-gray-400" />
      ) : (
        <FileText size={16} className="mr-2 text-gray-600 dark:text-gray-400" />
      )}

      {isEditing ? (
        <div className="flex items-center">
          <input
            ref={inputRef}
            type="text"
            value={editedName}
            onChange={(e) => setEditedName(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleSaveEdit}
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm min-w-0 max-w-[200px]"
            placeholder="Enter filename"
          />
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 ml-1"
            onClick={handleSaveEdit}
            aria-label="Save name"
          >
            <Check size={12} className="text-green-600 dark:text-green-400" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 ml-1"
            onClick={handleCancelEdit}
            aria-label="Cancel edit"
          >
            <XIcon size={12} className="text-gray-500 dark:text-gray-400" />
          </Button>
        </div>
      ) : (
        <div className="flex items-center">
          <span
            className="font-medium truncate max-w-[150px] sm:max-w-[200px] md:max-w-[250px] cursor-pointer hover:text-blue-600 dark:hover:text-blue-400"
            title={`${attachment.name} (click to edit)`}
            onClick={handleStartEdit}
          >
            {attachment.name}
          </span>
          {onRename && (
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1 opacity-0 group-hover:opacity-100 hover:opacity-100"
              onClick={handleStartEdit}
              aria-label="Edit name"
            >
              <Edit2 size={10} className="text-gray-500 dark:text-gray-400" />
            </Button>
          )}
        </div>
      )}

      <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
        ({Math.round(attachment.size / 1024)} KB)
      </span>

      {isImage && attachment.dataUrl && (
        <img
          src={attachment.dataUrl}
          alt={attachment.name}
          className="w-8 h-8 object-cover rounded ml-2 border border-gray-300 dark:border-gray-600"
        />
      )}

      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 ml-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
        onClick={() => onRemove(attachment.id)}
        aria-label={`Remove ${attachment.name}`}
      >
        <X size={14} className="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400" />
      </Button>
    </div>
  );
};